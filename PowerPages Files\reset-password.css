.reset-password-container {
    max-width: 600px;
}

#verificationCodeGroup {
    position: relative;
}

#verificationCode {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    letter-spacing: 2px;
    text-align: center;
    font-weight: 600;
}

#verificationCodeGroup .input-group {
    display: flex;
    position: relative;
}

#passwordForm .input-group .form-control {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#passwordForm .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.password-strength-indicator {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    text-align: center;
}

.password-strength-indicator.weak {
    background-color: var(--primary-red-light);
    color: var(--primary-red);
    border-left: 3px solid var(--primary-red);
}

.password-strength-indicator.medium {
    background-color: #fef3c7;
    color: #f59e0b;
    border-left: 3px solid #f59e0b;
}

.password-strength-indicator.strong {
    background-color: #f0f9ff;
    color: #059669;
    border-left: 3px solid #059669;
}

@media (max-width: 768px) {
    .reset-password-container {
        max-width: 95%;
    }

}

@media (max-width: 480px) {
    .reset-password-container {
        margin: 0.5rem auto;
        padding: 1rem;
    }
    
    .reset-password-container h2 {
        font-size: 1.25rem;
    }
    
    .reset-password-container h2::after {
        width: 40px;
        margin: 0.75rem auto;
    }
    
    #verificationCode {
        font-size: 0.95rem;
        letter-spacing: 1px;
    }
}

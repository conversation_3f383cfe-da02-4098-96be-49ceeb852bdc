.registration-container {
    max-width: 650px;
}

#registrationForm .input-group .form-control {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#registrationForm .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

#registrationForm .input-group .btn:hover {
    background-color: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--color-white);
}

#registrationForm .form-text.text-success {
    color: #28a745 !important;
}

#registrationForm .form-check-label a:hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

#submitButton {
    margin-top: 1rem;
}

#messageContent {
    padding: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.text-center.mt-3 {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-border);
}

@media (max-width: 768px) {
    .registration-container {
        max-width: 95%;
    }
}
